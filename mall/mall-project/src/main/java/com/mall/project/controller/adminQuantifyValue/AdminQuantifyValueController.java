package com.mall.project.controller.adminQuantifyValue;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.adminQuantifyValue.AdminQuantifyValue;
import com.mall.project.service.adminQuantifyValue.AdminQuantifyValueService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Admin量化值
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class AdminQuantifyValueController {

    @Autowired
    private AdminQuantifyValueService adminQuantifyValueService;

    /**
     * 查询Admin量化值, 分页显示
     */
    @PostMapping("/queryAdminAdminQuantifyValuePages")
    public CommonResult<CommonPage<Map<String, Object>>> queryAdminAdminQuantifyValuePages(@RequestBody @Valid AdminQuantifyValue param) {
        CommonPage<Map<String, Object>> commonPage = adminQuantifyValueService.queryAdminAdminQuantifyValuePages(param.getPhone(), param.getStartDate(), param.getEndDate(), param.getPageNum(), param.getPageSize());
        return CommonResult.success(commonPage);
    }


    /**
     * 导出Admin量化值 Excel
     */
    @PostMapping("/exportAdminAdminQuantifyValueExcel")
    public void exportAdminAdminQuantifyValueExcel(HttpServletResponse response,@RequestBody @Valid AdminQuantifyValue param) {
        try {
            // 获取Admin量化值数据
            List<Map<String, Object>> dataList = adminQuantifyValueService.exportAdminAdminQuantifyValueExcel(param.getPhone(),param.getStartDate(), param.getEndDate());

            // 获取汇总数据
            String todayAdminAdminQuantifyValue = adminQuantifyValueService.todayAdminAdminQuantifyValue(param.getPhone(),param.getStartDate());
            String totalAdminAdminQuantifyValue = adminQuantifyValueService.totalAdminAdminQuantifyValue(param.getPhone(),param.getStartDate());

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateDate", "updateDate");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("value", "value");

            // 配置汇总信息
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                // 第一行：2个统计项
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日Admin量化值")
                        .value(todayAdminAdminQuantifyValue)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计Admin量化值")
                        .value(totalAdminAdminQuantifyValue)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<AdminQuantifyValue> config = UniversalExcelExporter.ExportConfig.<AdminQuantifyValue>builder()
                    .dataList(dataList)
                    .entityClass(AdminQuantifyValue.class)
                    .fileName("Admin量化值")
                    .sheetName("Admin量化值")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出Admin量化值异常", e);
        }
    }
}
