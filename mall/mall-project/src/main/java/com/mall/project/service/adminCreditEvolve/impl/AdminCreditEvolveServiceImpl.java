package com.mall.project.service.adminCreditEvolve.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.adminCreditEvolve.AdminCreditEvolveDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.adminCreditEvolve.AdminCreditEvolveService;
import com.mall.project.util.MallBAuthUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Admin量化进化量 实现类
 */
@Service
@Slf4j
public class AdminCreditEvolveServiceImpl implements AdminCreditEvolveService {

    @Autowired
    private AdminCreditEvolveDao adminCreditEvolveDao;

    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    /**
     * 去mallB 读取 Admin量化进化量  数据
     */
    @Override
    public void getAdminCreditEvolveFromMallB() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();
            ResponseEntity<String> adminCreditResponse = mallBAuthUtils.getForEntity("/mall/receptionA/statistics/adminFaith", String.class);

            // 检查获取Admin量化进化量数据是否成功
            if (adminCreditResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB系统Admin量化进化量数据失败: " + adminCreditResponse.getStatusCode());
            }

            // 解析Admin量化进化量数据响应
            String adminCreditResponseBody = adminCreditResponse.getBody();
            if (adminCreditResponseBody == null) {
                throw new BusinessException("获取mallB系统Admin量化进化量数据响应为空");
            }

            // 解析响应JSON
            JsonNode adminCreditRoot = objectMapper.readTree(adminCreditResponseBody);

            if (adminCreditRoot.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB系统Admin量化进化量数据失败: " + adminCreditRoot.get("msg").asText());
            }

            // 获取Admin量化进化量数据
            JsonNode dataNode = adminCreditRoot.path("data");
            if (dataNode.isMissingNode() || dataNode.isNull()) {
                log.info("今日mallB系统无Admin量化进化量数据");
                return;
            }

            // 从data节点中提取数据
            String phone = dataNode.path("phone").asText();
            double value = dataNode.path("dayFaithAmount").asDouble();
            String updateDate = dataNode.path("date").asText();

            //log.info("开始处理并存储mallB Admin量化进化量数据: phone={}, value={}, date={}", phone, value, updateDate);

            // 最后保存数据
            adminCreditEvolveDao.saveOrUpdateAdminCreditEvolve(phone, String.valueOf(value), updateDate);

            //log.info("成功完成mallB系统Admin量化进化量数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }


    /**
     * 查询Admin量化进化量, 分页显示
     */
    @Override
    public CommonPage<Map<String, Object>> queryAdminCreditEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = adminCreditEvolveDao.queryAdminCreditEvolvePages(phone, startDate, endDate, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedDataList = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = adminCreditEvolveDao.totalAdminCreditEvolveCount(phone, startDate, endDate);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        AdminCreditEvolveServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new AdminCreditEvolveServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedDataList);
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("todayTotalAdminCreditEvolve", todayTotalAdminCreditEvolve(phone, startDate));   //今日Admin量化进化量
        summary.put("totalAdminCreditEvolve", totalAdminCreditEvolve(phone, startDate));   //累计Admin量化进化量
        commonPage.setSummary(summary);
        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }

    /**
     * 今日Admin累计量化进化量
     */
    @Override
    public String todayTotalAdminCreditEvolve(String phone,String startTime) {
        return adminCreditEvolveDao.todayTotalAdminCreditEvolve(phone,startTime) == null ? "0" : adminCreditEvolveDao.todayTotalAdminCreditEvolve(phone, startTime);
    }
    /**
     * 累计Admin量化进化量
     */
    @Override
    public String totalAdminCreditEvolve(String phone,String startTime) {
        return adminCreditEvolveDao.totalAdminCreditEvolve(phone,startTime) == null ? "0" : adminCreditEvolveDao.totalAdminCreditEvolve(phone, startTime);
    }

    /**
     * 导出Admin量化进化量 Excel
     */
    @Override
    public List<Map<String, Object>> exportAdminCreditEvolveExcel(String phone,String startDate, String endDate) {
        if(startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if(endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 转换下划线格式为驼峰格式
        return adminCreditEvolveDao.exportAdminCreditEvolveExcel(phone,startDate, endDate).stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }
}
