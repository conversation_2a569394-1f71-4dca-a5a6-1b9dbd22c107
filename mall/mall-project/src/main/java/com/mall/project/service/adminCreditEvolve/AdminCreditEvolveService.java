package com.mall.project.service.adminCreditEvolve;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * Admin量化进化量 服务接口
 */
public interface AdminCreditEvolveService {

    /**
     * 去mallB 读取 Admin量化进化量  数据
     */
    public void getAdminCreditEvolveFromMallB();

    /**
     * 查询Admin量化进化量, 分页显示
     */
    public CommonPage<Map<String, Object>> queryAdminCreditEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize);

    /**
     * 今日Admin累计量化进化量
     */
    public String todayTotalAdminCreditEvolve(String phone,String startTime);

    /**
     * Admin累计量化进化量
     */
    public String totalAdminCreditEvolve(String phone,String startTime);

    /**
     * 导出Admin量化进化量 Excel
     */
    public List<Map<String, Object>> exportAdminCreditEvolveExcel(String phone, String startDate, String endDate);
}
