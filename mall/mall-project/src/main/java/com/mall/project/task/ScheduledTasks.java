package com.mall.project.task;

import com.mall.project.config.SchedulingConfig;
import com.mall.project.service.adminQuantifyValue.AdminQuantifyValueService;
import com.mall.project.service.creditEvolve.CreditEvolveService;
import com.mall.project.service.functionDatas.FunctionDatasService;
import com.mall.project.service.mallBUsers.GetMallBUsersService;
import com.mall.project.service.quantifyCount.QuantifyCountService;
import com.mall.project.service.quantizationValue.QuantizationValueService;
import com.mall.project.service.systemInfo.SystemInfoService;
import com.mall.project.service.writeOffData.WriteOffDataService;
import com.mall.project.task.executor.DataSyncTaskExecutor;
import com.mall.project.task.executor.PushTaskExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 定时任务调度器
 * 负责协调各种定时任务的执行
 */
@Slf4j
@Component
public class ScheduledTasks {
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private SchedulingConfig schedulingConfig;

    @Autowired
    private DataSyncTaskExecutor dataSyncTaskExecutor;

    @Autowired
    private PushTaskExecutor pushTaskExecutor;

    @Autowired
    private SystemInfoService systemInfoService;

    @Autowired
    private QuantifyCountService quantifyCountService;

    @Autowired
    private FunctionDatasService functionDatasService;

    @Autowired
    private QuantizationValueService quantizationValueService;

    @Autowired
    private WriteOffDataService writeOffDataService;

    @Autowired
    private GetMallBUsersService getMallBUsersService;
    @Autowired
    private CreditEvolveService creditEvolveService;

    @Autowired
    private AdminQuantifyValueService adminQuantifyValueService;

    /**
     * 固定间隔执行的定时任务，每5分钟执行一次
     * 目前已禁用，如需启用请取消注释并配置相应的业务逻辑
     */
    @Scheduled(fixedRate = 300000)
    public void reportCurrentTime() {
        log.trace("定时任务检查点(每5分钟): {}", LocalDateTime.now().format(DATE_FORMAT));
        pushTaskExecutor.executeDailyPushTasks();
        //dataSyncTaskExecutor.executeDataSyncTasks();
        //quantizationValueService.updateQuantizationValue();
        //creditEvolveService.getCreditEvolveFromMallB();
        //functionDatasService.updateQuantifyCount();
        //systemInfoService.updateSystemInfo();
        //adminQuantifyValueService.updateAdminQuantifyValue("");
    }

    /**
     * 每日推送任务 - 凌晨00:05执行
     * 主要负责向mallB系统推送各种配置和数据
     */
    @Scheduled(cron = "${task.scheduling.daily-task-cron}")
    public void dailyPushTask() {
        log.info("每日推送任务开始执行: {}, cron表达式: {}",
                LocalDateTime.now().format(DATE_FORMAT),
                schedulingConfig.getDailyTaskCron());

        try {
            pushTaskExecutor.executeDailyPushTasks();
            log.info("每日推送任务执行完成");
        } catch (Exception e) {
            log.error("每日推送任务执行失败", e);
        }
    }

    /**
     * 每日数据同步任务 - 凌晨01:00执行
     * 主要负责从mallB系统同步数据并更新本地数据
     */
    @Scheduled(cron = "${task.scheduling.hourly-task-cron}")
    public void dailyDataSyncTask() {
        log.info("每日数据同步任务开始执行: {}, cron表达式: {}",
                LocalDateTime.now().format(DATE_FORMAT),
                schedulingConfig.getHourlyTaskCron());

        try {
            dataSyncTaskExecutor.executeDataSyncTasks();
            log.info("每日数据同步任务执行完成");
        } catch (Exception e) {
            log.error("每日数据同步任务执行失败", e);
        }
    }
}
