package com.mall.project.dao.adminCreditEvolve;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Admin量化进化量 数据访问层
 */
@Repository
@Slf4j
public class AdminCreditEvolveDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Admin量化进化量更新或保存
     */
    public void saveOrUpdateAdminCreditEvolve(String phone, String value, String updateDate) {
        // 先查询是否存在相同phone和date的记录
        String checkSql = "SELECT EXISTS(SELECT 1 FROM admin_Credit_Evolve WHERE phone = ? AND update_date = CURDATE())";
        Boolean exists = jdbcTemplate.queryForObject(checkSql, Boolean.class, phone);
        
        if (exists) {
            // 如果存在，则更新
            String updateSql = "UPDATE admin_Credit_Evolve SET value = ? WHERE phone = ? AND update_date = CURDATE()";
            jdbcTemplate.update(updateSql, value, phone);
        } else {
            // 如果不存在，则插入
            String insertSql = "INSERT INTO admin_Credit_Evolve (phone, value, update_date) VALUES (?, ?, CURDATE())";
            jdbcTemplate.update(insertSql, phone, value);
        }
    }
    /**
     * 今日Admin累计量化进化量
     */
    public String todayTotalAdminCreditEvolve(String phone,String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(SUM(value),0) as value FROM admin_Credit_Evolve WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startTime);
        }else{
            sql += " AND update_date = CURDATE() ";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

     /**
     * Admin累计量化进化量
     */
    public String totalAdminCreditEvolve(String phone,String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(SUM(value),0) as value FROM admin_Credit_Evolve WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND update_date <= ?";
            params.add(startTime);
        }else{
            sql += " AND update_date <= CURDATE() ";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    /**
     * 查询Admin量化进化量, 分页显示
     */
    public List<Map<String, Object>> queryAdminCreditEvolvePages(String phone, String startDate, String endDate, int limit, int offset) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT phone, value, update_date FROM admin_Credit_Evolve WHERE 1=1");
        
        List<Object> params = new ArrayList<>();
        
        if (phone != null && !phone.trim().isEmpty()) {
            sql.append(" AND phone like ?");
            params.add("%" + phone + "%");
        }
        
        if (startDate != null && !startDate.trim().isEmpty()) {
            sql.append(" AND update_date = ?");
            params.add(startDate);
        }else{
            sql.append(" AND update_date <= CURDATE() ");
        }
        
        sql.append(" ORDER BY update_date DESC,id DESC LIMIT ? OFFSET ?");
        params.add(limit);
        params.add(offset);
        
        return jdbcTemplate.queryForList(sql.toString(), params.toArray());
    }

    /**
     * 查询Admin量化进化量总数，用于分页
     */
    public long totalAdminCreditEvolveCount(String phone, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) FROM admin_Credit_Evolve WHERE 1=1");
        
        List<Object> params = new ArrayList<>();
        
        if (phone != null && !phone.trim().isEmpty()) {
            sql.append(" AND phone like ?");
            params.add("%" + phone + "%");
        }
        
        if (startDate != null && !startDate.trim().isEmpty()) {
            sql.append(" AND update_date = ?");
            params.add(startDate);
        }else{
            sql.append(" AND update_date <= CURDATE() ");
        }
        
        Long count = jdbcTemplate.queryForObject(sql.toString(), Long.class, params.toArray());
        return count != null ? count : 0L;
    }

    /**
     * 导出Admin量化进化量 Excel
     */
    public List<Map<String, Object>> exportAdminCreditEvolveExcel(String phone,String startDate, String endDate) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT phone, value, update_date FROM admin_Credit_Evolve WHERE 1=1");
        
        List<Object> params = new ArrayList<>();

        if (phone != null && !phone.trim().isEmpty()) {
            sql.append(" AND phone like ?");
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.trim().isEmpty()) {
            sql.append(" AND update_date = ?");
            params.add(startDate);
        }else{
            sql.append(" AND update_date <= CURDATE() ");
        }
        
        sql.append(" ORDER BY update_date DESC");
        
        return jdbcTemplate.queryForList(sql.toString(), params.toArray());
    }
}
