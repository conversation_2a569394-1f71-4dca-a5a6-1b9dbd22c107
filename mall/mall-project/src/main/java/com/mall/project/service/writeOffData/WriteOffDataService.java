package com.mall.project.service.writeOffData;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * 核销数据服务接口
 */
public interface WriteOffDataService {

    /**
     * 获取mallB系统的核销数据
     */
    public void getWriteOffDataFromMallB();

    /**
     * 获取mallB系统的促销金数据
     */
    public void getPromotionDataFromMallB();


    /**
     * 从mallB系统获取核销值数据
     */
    public void getWriteOffGoldFromMallB();

    /**
     * 查询核销数据,分页显示
     */
    public CommonPage<Map<String, Object>> queryWriteOffDataPages(String phone, String startDate, String endDate, int pageNum, int pageSize);

    /**
     * 导出核销数据 Excel
     */
    public List<Map<String, Object>> exportWriteOffDataExcel(String phone, String startDate, String endDate);

    /**
     * 统计 今日核销补贴金
     */
    public String todayTotalWriteOffSubsidy(String phone,String startDate);

    /**
     * 统计 累计已核销补贴金
     */
    public String totalWriteOffSubsidy(String phone,String startDate);

    /**
     * 统计 累计未核销
     */
    public String totalUnWriteOffSubsidy(String phone,String startDate);

    /**
     * 今日已核销促销金
     */
    public String todayPromotionUsed(String phone,String startDate);

    /**
     * 总累计使用金额
     */
    public String totalPromotionUsed(String phone,String startDate);

    /**
     * 累计核销值
     */
    public String totalWriteOffGold(String phone,String startDate);
}
