package com.mall.project.controller.mallBUsers;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.mallBUsers.MallBUsers;
import com.mall.project.service.mallBUsers.GetMallBUsersService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class GetMallBUsersController {

    @Autowired
    private GetMallBUsersService getMallBUsersService;

    @GetMapping("/getMallBUsers1")
    public CommonResult<Map<String, Object>> GetMallBUsers() {
        getMallBUsersService.getMallBUsers();
        return CommonResult.success(null);
    }


    @PostMapping("/getMallBUsers")
    public CommonResult<CommonPage<Map<String, Object>>> queryMallBUsers(@RequestBody @Valid MallBUsers mallBUsers) {
        CommonPage<Map<String, Object>> commonPage = getMallBUsersService.getMallBUsers(mallBUsers.getPhone(),mallBUsers.getBusinessLicense(), mallBUsers.getPageNum(), mallBUsers.getPageSize());
        return CommonResult.success(commonPage);
    }

    // 更新用户状态
    @PostMapping("/updateMallBUsersStatus")
    public CommonResult<String> updateMallBUsersStatus(@RequestBody @Valid MallBUsers mallBUsers) {
        int result = getMallBUsersService.updateMallBUsersStatus(mallBUsers.getId(), mallBUsers.getStatus());
        if (result > 0) {
            return CommonResult.success("更新成功");
        }else{
            return CommonResult.failed("更新失败");
        }
    }
}
