package com.mall.project.service.systemInfo.impl;

import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.systemInfo.SystemInfoDao;
import com.mall.project.service.quantifyCount.QuantifyCountService;
import com.mall.project.service.systemInfo.SystemInfoService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 系统信息服务实现类
 */
@Service
public class SystemInfoServiceImpl implements SystemInfoService {

    @Autowired
    private SystemInfoDao systemInfoDao;

    @Autowired
    private QuantifyCountService quantifyCountService;

    @Override
    public void updateSystemInfo() {
        Map<String, Object> systemInfo = systemInfoDao.getSystemInfo();
        if(isNotEmpty(systemInfo)){
            // 每日数据累计量
            String dailyDataAccrue = systemInfo.get("everyday_count").toString();
            // 每日计量数
            String dailyMeterage = systemInfo.get("result").toString();
            // 每日余数
            String dailyRemainder = new BigDecimal(dailyDataAccrue).subtract(new BigDecimal(dailyMeterage)).setScale(2, BigDecimal.ROUND_DOWN).toString();
            //每日量化数总累计
            //String dailyQuantityTotal = quantifyCountService.sumWeightCountTotal("");   //原来拿的是这个,2025-08-07 修改为以下:
            String dailyQuantityTotal = systemInfo.get("everyday_quantity").toString();
            systemInfoDao.updateSystemInfo(dailyDataAccrue, dailyRemainder, dailyQuantityTotal,dailyMeterage);
        }
    }

    /**
     * 查询系统信息, 分页显示
     */
    @Override
    public CommonPage<Map<String, Object>> querySystemInfoPages(String startDate, String endDate, int pageNum, int pageSize) {
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> list = systemInfoDao.querySystemInfoPages(startDate, endDate, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = list.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = systemInfoDao.totalSystemInfo(startDate, endDate);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        SystemInfoServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new SystemInfoServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        // 每日数据累计量
        summary.put("dailyDataAccrueTotal", dailyDataAccrueTotal(startDate));
        // 系统每日余数
        summary.put("dailyRemainderTotal", dailyRemainderTotal(startDate));
        // 每日系统量化数总累计
        summary.put("dailyQuantityTotal", dailyQuantityTotal(startDate));
        // 累计计量数
        summary.put("dailyMeterageTotal", dailyMeterageTotal(startDate));
        commonPage.setSummary(summary);
        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }

    /**
     * 统计每日数据累计量
     */
    public String dailyDataAccrueTotal(String startDate){
        // 转换下划线格式为驼峰格式
        return ConvertToCamelCase.formatDecimal(new BigDecimal(systemInfoDao.dailyDataAccrueTotal(startDate)));
    }

    /**
     * 统计 系统每日余数
     */
    public String dailyRemainderTotal(String startDate){
        // 转换下划线格式为驼峰格式
        return ConvertToCamelCase.formatDecimal(new BigDecimal(systemInfoDao.dailyRemainderTotal(startDate)));
    }

    /**
     * 统计每日系统量化数总累计
     */
    public String dailyQuantityTotal(String startDate){
        // 转换下划线格式为驼峰格式
        return ConvertToCamelCase.formatDecimal(new BigDecimal(systemInfoDao.dailyQuantityTotal(startDate)));
    }
    /**
     * 统计每日计量数
     */
    public String dailyMeterageTotal(String startDate){
        // 转换下划线格式为驼峰格式
        return ConvertToCamelCase.formatDecimal(new BigDecimal(systemInfoDao.dailyMeterageTotal(startDate)));
    }
}
