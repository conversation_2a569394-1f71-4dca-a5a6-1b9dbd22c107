package com.mall.project.dao.systemInfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 系统信息服务数据访问对象
 */
@Repository
public class SystemInfoDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询系统信息, 分页显示
     */
    public List<Map<String, Object>> querySystemInfoPages(String startDate, String endDate, int limit, int offset) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT update_date,daily_data_accrue,daily_remainder,daily_quantity_total,daily_meterage FROM system_info WHERE 1=1";
        if (isNotEmpty(startDate)) {
            sql += " AND DATE(update_date) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(update_date) <= CURDATE() ";
        }
        sql += " ORDER BY update_date DESC,id DESC LIMIT " + limit + " OFFSET " + offset;
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }
    /**
     * 查询系统信息总条数
     */
    public int totalSystemInfo(String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COUNT(*) FROM system_info WHERE 1=1";
        if (isNotEmpty(startDate)) {
            sql += " AND DATE(update_date) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(update_date) <= CURDATE() ";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, Integer.class);
        }else{
            return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        }
    }

    /**
     * 每日数据累计量
     */
    public String dailyDataAccrueTotal(String startDate){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(daily_data_accrue, 0) as daily_data_accrue FROM system_info where 1=1";
            if (isNotEmpty(startDate)) {
                sql += " AND DATE(update_date) = ?";
                params.add(startDate);
            }else{
                sql += " AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (DataAccessException e) {
            return "0";
        }
    }

    /**
     * 系统每日余数
     */
    public String dailyRemainderTotal(String startDate){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(daily_remainder,0) as daily_remainder  FROM system_info where 1 =1 ";
            if (isNotEmpty(startDate)) {
                sql += " AND DATE(update_date) = ?";
                params.add(startDate);
            }else{
                sql += " AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (DataAccessException e) {
            return "0";
        }
    }

    /**
     * 统计每日系统量化数总累计
     */
    public String dailyQuantityTotal(String startDate){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(daily_quantity_total, 0) as daily_quantity_total FROM system_info where 1 = 1";
            if (isNotEmpty(startDate)) {
                sql += " AND DATE(update_date) = ?";
                params.add(startDate);
            }else{
                sql += " AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (DataAccessException e) {
            return "0";
        }
    }

    /**
     * 统计累计计量数
     */
    public String dailyMeterageTotal(String startDate){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(daily_meterage), 0) as daily_meterage_total FROM system_info where 1 = 1";
            if (isNotEmpty(startDate)) {
                sql += " AND DATE(update_date) <= ?";
                params.add(startDate);
            }else{
                sql += " AND DATE(update_date) <= CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (DataAccessException e) {
            return "0";
        }
    }

    /**
     * 查询每日系统信息
     */
    public Map<String, Object> getSystemInfo() {
        try {
            String sql = "SELECT everyday_count,result,everyday_quantity FROM Custom_constants WHERE id = 1";
            return jdbcTemplate.queryForMap(sql);
        } catch (DataAccessException e) {
            return null;
        }
    }
    /**
     * 更新每日系统信息
     */
    public void updateSystemInfo(String dailyDataAccrue,String dailyRemainder,String dailyQuantityTotal,String dailyMeterage) {
        String sql = "SELECT EXISTS(SELECT 1 FROM system_info WHERE DATE(update_date) = CURDATE())";
        if (jdbcTemplate.queryForObject(sql, Integer.class) == 0) {
            sql = "INSERT INTO system_info(daily_data_accrue,daily_remainder,daily_quantity_total,daily_meterage,update_date)VALUES(?, ?, ?, ?, CURDATE())";
        }else{
            sql = "UPDATE system_info SET daily_data_accrue = ?,daily_remainder = ?,daily_quantity_total = ?,daily_meterage = ? WHERE DATE(update_date) = CURDATE()";
        }
        jdbcTemplate.update(sql, dailyDataAccrue, dailyRemainder,dailyQuantityTotal,dailyMeterage);
    }

    /**
     * 每日自动更新合计
     */
    public String everydayCount() {
        String sql = "SELECT sum(trade_amount) as trade_amount_total from enterprise_data_name where type = 0";
        return jdbcTemplate.queryForObject(sql, String.class);
    }
    /**
     * 获取自定义常数
     */
    public String getCustomConstants() {
        String sql = "SELECT constants FROM Custom_constants WHERE id = 1";
        return jdbcTemplate.queryForObject(sql, String.class);
    }
}
