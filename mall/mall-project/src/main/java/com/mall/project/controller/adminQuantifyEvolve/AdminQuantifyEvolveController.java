package com.mall.project.controller.adminQuantifyEvolve;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.adminQuantifyEvolve.AdminQuantifyEvolve;
import com.mall.project.service.adminQuantifyEvolve.AdminQuantifyEvolveService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Admin量化值进化量 控制器
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class AdminQuantifyEvolveController {

    @Autowired
    private AdminQuantifyEvolveService adminQuantifyEvolveService;

    /**
     * 查询Admin量化值进化量, 分页显示
     */
    @PostMapping("/queryAdminAdminQuantifyEvolvePages")
    public CommonResult<CommonPage<Map<String, Object>>> queryAdminAdminQuantifyEvolvePages(@RequestBody @Valid AdminQuantifyEvolve param) {
        CommonPage<Map<String, Object>> commonPage = adminQuantifyEvolveService.queryAdminAdminQuantifyEvolvePages(param.getPhone(), param.getStartDate(), param.getEndDate(), param.getPageNumOrDefault(), param.getPageSizeOrDefault());
        return CommonResult.success(commonPage);
    }

    /**
     * 导出Admin量化值进化量 Excel
     */
    @PostMapping("/exportAdminAdminQuantifyEvolveExcel")
    public void exportAdminAdminQuantifyEvolveExcel(HttpServletResponse response,@RequestBody @Valid AdminQuantifyEvolve param) {
        try {
            // 获取Admin量化值进化量数据
            List<Map<String, Object>> dataList = adminQuantifyEvolveService.exportAdminAdminQuantifyEvolveExcel(param.getPhone(),param.getStartDate(), param.getEndDate());

            // 获取汇总数据
            String todayTotalAdminQuantifyEvolve = adminQuantifyEvolveService.todayTotalAdminQuantifyEvolve(param.getPhone(),param.getStartDate());
            String totalAdminQuantifyEvolve = adminQuantifyEvolveService.totalAdminQuantifyEvolve(param.getPhone(),param.getStartDate());

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateDate", "updateDate");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("value", "value");

            // 配置汇总信息
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                // 第一行：2个统计项
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日Admin量化值进化量")
                        .value(todayTotalAdminQuantifyEvolve)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计Admin量化值进化量")
                        .value(totalAdminQuantifyEvolve)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<AdminQuantifyEvolve> config = UniversalExcelExporter.ExportConfig.<AdminQuantifyEvolve>builder()
                    .dataList(dataList)
                    .entityClass(AdminQuantifyEvolve.class)
                    .fileName("Admin量化值进化量")
                    .sheetName("Admin量化值进化量")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出Admin量化值进化量异常", e);
        }
    }
}
