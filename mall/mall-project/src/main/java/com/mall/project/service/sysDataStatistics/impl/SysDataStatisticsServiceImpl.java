package com.mall.project.service.sysDataStatistics.impl;

import com.mall.project.dao.sysDataStatistics.SysDataStatisticsDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.sysDataStatistics.SysDataStatisticsService;
import com.mall.project.util.DynamicMethodInvoker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 系统数据统计服务实现类
 */
@Service
@Slf4j
public class SysDataStatisticsServiceImpl implements SysDataStatisticsService {

    @Autowired
    private SysDataStatisticsDao sysDataStatisticsDao;

    @Autowired
    private DynamicMethodInvoker dynamicMethodInvoker;

    // 操作类型常量
    private static final String OPERATION_ADD = "add";
    private static final String OPERATION_QUERY = "query";

    // 数据类型常量
    private static final int TYPE_TRADE = 0;
    private static final int TYPE_QUANTITY = 1;

    // 需要传递type参数的方法表达式
    private static final Set<String> METHODS_WITH_TYPE_PARAM = Set.of(
        "cooperateEnterpriseService.todayTotalTrade()",
        "cooperateEnterpriseService.todayTotalCount()",
        "cooperateEnterpriseService.cumulativeTotalTrade()"
    );

    /**
     * 根据统计名称查询计算方法
     */
    private String getCalculateMethodByDataTypeName(String dataTypeName) {
        return sysDataStatisticsDao.getCalculateMethodByDataTypeName(dataTypeName);
    }

    /**
     * 系统数据统计
     */
    @Override
    public Map<String, Object> statisticsData(String operate, String type, String dataTypeName) {
        // 参数验证
        String validationError = validateParameters(operate, type, dataTypeName);
        if (validationError != null) {
            throw new BusinessException(validationError);
        }

        int typeInt = Integer.parseInt(type);

        switch (operate) {
            case OPERATION_ADD:
                return processAddOperation(dataTypeName, typeInt);
            case OPERATION_QUERY:
                return processQueryOperation(typeInt);
            default:
                throw new BusinessException("操作类型不合法!");
        }
    }

    /**
     * 参数验证
     * @return 验证失败时返回错误信息，验证成功返回null
     */
    private String validateParameters(String operate, String type, String dataTypeName) {
        if (operate == null || operate.trim().isEmpty()) {
            return "操作类型不能为空!";
        }
        if (type == null || type.trim().isEmpty()) {
            return "数据类型不能为空!";
        }
        try {
            Integer.parseInt(type);
        } catch (NumberFormatException e) {
            return "数据类型格式不正确!";
        }
        if (OPERATION_ADD.equals(operate) && (dataTypeName == null || dataTypeName.trim().isEmpty())) {
            return "数据类型名称不能为空!";
        }
        return null; // 验证通过
    }

    /**
     * 处理添加操作
     */
    private Map<String, Object> processAddOperation(String dataTypeName, int type) {
        // 检查数据名称是否已存在
        if (sysDataStatisticsDao.isDataNameExists(dataTypeName, type)) {
            throw new BusinessException("数据名称已经存在不能重复添加!");
        }

        // 获取计算方法并执行
        String methodExpression = getCalculateMethodByDataTypeName(dataTypeName);
        String result = invokeMethodWithTypeCheck(methodExpression, type);

        // 插入数据
        sysDataStatisticsDao.insertDataToEnterpriseDataName(dataTypeName, result, type);

        Map<String, Object> dataMapResult = new HashMap<>();
        dataMapResult.put("tradeName", dataTypeName);
        dataMapResult.put("trade_amount", result);
        return dataMapResult;
    }

    /**
     * 处理查询操作
     */
    private Map<String, Object> processQueryOperation(int type) {
        List<Map<String, Object>> enterpriseDataNames = sysDataStatisticsDao.getEnterpriseDataName(type);

        // 批量更新数据
        updateEnterpriseDataBatch(enterpriseDataNames, type);

        // 根据类型处理后续逻辑
        return processDataByType(type);
    }

    /**
     * 批量更新企业数据
     */
    private void updateEnterpriseDataBatch(List<Map<String, Object>> enterpriseDataNames, int type) {
        for (Map<String, Object> dataMap : enterpriseDataNames) {
            String tradeName = dataMap.get("trade_name").toString();
            String methodExpression = getCalculateMethodByDataTypeName(tradeName);
            String result = invokeMethodWithTypeCheck(methodExpression, type);
            sysDataStatisticsDao.updateDataInEnterpriseDataName(tradeName, result, type);
        }
    }

    /**
     * 根据数据类型处理数据
     */
    private Map<String, Object> processDataByType(int type) {
        List<Map<String, Object>> enterpriseDataNameList = sysDataStatisticsDao.getEnterpriseDataNameList(type);

        if (type == TYPE_TRADE) {
            return processTradeTypeData(enterpriseDataNameList);
        } else if (type == TYPE_QUANTITY) {
            return processQuantityTypeData(enterpriseDataNameList);
        }

        return new HashMap<>();
    }

    /**
     * 处理交易类型数据 (type = 0)
     */
    private Map<String, Object> processTradeTypeData(List<Map<String, Object>> enterpriseDataNameList) {
        double everyDayCount = calculateTotalAmount(enterpriseDataNameList);

        // 获取自定义常量并计算结果
        String customConstants = sysDataStatisticsDao.getCustomConstants();
        BigDecimal resultValue = new BigDecimal(everyDayCount).divide(new BigDecimal(customConstants), 6, RoundingMode.DOWN);

        // 更新自定义常量
        sysDataStatisticsDao.updateCustomConstants(String.valueOf(everyDayCount), resultValue.toString());

        Map<String, Object> dataMapResult = new HashMap<>();
        dataMapResult.put("enterpriseDataName", enterpriseDataNameList);
        dataMapResult.put("tradeAmountTotal", everyDayCount);
        return dataMapResult;
    }

    /**
     * 处理数量类型数据 (type = 1)
     */
    private Map<String, Object> processQuantityTypeData(List<Map<String, Object>> enterpriseDataNameList) {
        double everyDayQuantity = calculateTotalAmount(enterpriseDataNameList);

        // 更新数量常量
        sysDataStatisticsDao.updateCustomConstantsForQuantity(String.valueOf(everyDayQuantity));

        Map<String, Object> dataMapResult = new HashMap<>();
        dataMapResult.put("enterpriseDataName", enterpriseDataNameList);
        dataMapResult.put("tradeAmountTotal", everyDayQuantity);
        return dataMapResult;
    }

    /**
     * 计算总金额
     */
    private double calculateTotalAmount(List<Map<String, Object>> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return 0.0;
        }

        BigDecimal total = dataList.stream()
            .map(map -> {
                Object amount = map.get("trade_amount");
                if (amount != null) {
                    return new BigDecimal(amount.toString());
                } else {
                    return BigDecimal.ZERO;
                }
            })
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 直接截断到两位小数
        return total.setScale(2, RoundingMode.DOWN).doubleValue();
    }

    /**
     * 根据方法表达式调用方法，自动判断是否需要传递type参数
     */
    private String invokeMethodWithTypeCheck(String methodExpression, int type) {
        if (methodExpression == null || methodExpression.trim().isEmpty()) {
            //log.warn("方法表达式为空，返回默认值0");
            return "0";
        }

        if (METHODS_WITH_TYPE_PARAM.contains(methodExpression)) {
            return dynamicMethodInvoker.invokeMethod(methodExpression, type);
        } else {
            return dynamicMethodInvoker.invokeMethod(methodExpression);
        }
    }
}
