package com.mall.project.controller;

import com.mall.common.api.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 用于验证服务是否正常运行
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class HealthController {

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public CommonResult<Map<String, Object>> health() {
        log.info("健康检查接口被调用");
        
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", LocalDateTime.now());
        result.put("service", "mall-project");
        result.put("message", "服务运行正常");
        
        return CommonResult.success(result);
    }
    
    /**
     * 测试导出接口路由
     */
    @GetMapping("/test-export")
    public CommonResult<String> testExport() {
        log.info("测试导出接口被调用");
        return CommonResult.success("导出接口路由正常");
    }
}
