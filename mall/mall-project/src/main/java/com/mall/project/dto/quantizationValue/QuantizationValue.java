package com.mall.project.dto.quantizationValue;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 描述：量化值实体类
 */
@Data
public class QuantizationValue {

    @ExcelProperty("日期")
    private String updateDate;             //更新日期
    @ExcelProperty("手机号")
    private String phone;                  //手机号
    @ExcelProperty("名字")
    private String name;                   //名字
    @ExcelProperty("量化值")
    private String value;                  //量化值
    @ExcelProperty("累计量化值")
    private String totalValue;             //累计量化值
    @ExcelProperty("信用值")
    private String creditValue;            //信用值
    @ExcelProperty("累计信用值")
    private String totalCreditValue;       //累计信用值
    @ExcelProperty("平台补贴金/促销金")
    private String platformGold;           //平台金
    @ExcelProperty("累计平台金")
    private String totalPlatformGold;      //累计平台金

    @ExcelIgnore  //Excel导出时忽略此字段
    private String startDate;              //查询开始日期
    @ExcelIgnore  //Excel导出时忽略此字段
    private String endDate;                //查询结束日期

    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageNum;
    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
