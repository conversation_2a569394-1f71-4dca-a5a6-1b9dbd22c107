package com.mall.project.dto.adminQuantifyValue;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * Admin量化值
 */
@Data
public class AdminQuantifyValue {

    @ExcelProperty("日期")
    private String updateDate;

    @ExcelProperty("手机号")
    private String phone;

    @ExcelProperty("量化值")
    private String value;


    @ExcelIgnore  //Excel导出时忽略此字段
    private String startDate;              //查询开始日期
    @ExcelIgnore  //Excel导出时忽略此字段
    private String endDate;                //查询结束日期

    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageNum;
    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
