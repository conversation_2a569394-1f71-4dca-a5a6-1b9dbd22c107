package com.mall.project.service.dailyTradePercentage.impl;

import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.dailyTradePercentage.DailyTradePercentageDao;
import com.mall.project.dto.dailyTradePercentage.DailyTradePercentage;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.dailyTradePercentage.DailyTradePercentageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * 所有企业各ID每日每笔新交易数据的量化数比配置
 */
@Service
@Slf4j
public class DailyTradePercentageServiceImpl implements DailyTradePercentageService {

    @Autowired
    private DailyTradePercentageDao dailyTradePercentageDao;
    /**
     * 获取所有企业各ID每日每笔新交易数据的量化数比配置
     */
    @Override
    public Map<String, Object> getDailyTradePercentage() {
        Map<String, Object> dataMap = dailyTradePercentageDao.getDailyTradePercentage();
        // 转换下划线格式为驼峰格式
        return ConvertToCamelCase.convertToCamelCase(dataMap);
    }

    /**
     * 更新所有企业各ID每日每笔新交易数据的量化数比配置
     */
    public void updateDailyTradePercentage() {
        Map<String, Object> dataMap = dailyTradePercentageDao.getDailyTradePercentage();
        if(!dataMap.isEmpty()){
            BigDecimal dailyTradePercentage = new BigDecimal(dataMap.get("daily_trade_percentage").toString());
            Integer ranking1 = Integer.parseInt(dataMap.get("ranking1").toString());
            BigDecimal ranking1Percentage = new BigDecimal(dataMap.get("ranking1_percentage").toString());
            Integer ranking2 = Integer.parseInt(dataMap.get("ranking2").toString());
            BigDecimal ranking2Percentage = new BigDecimal(dataMap.get("ranking2_percentage").toString());
            dailyTradePercentageDao.updateEnterpriseProductData(dailyTradePercentage);  //按 所有企业各ID每日每笔新交易数据的量化数比 更新企业产品数据今日量化数
            dailyTradePercentageDao.updateEnterpriseProductDataByRanking(ranking1, ranking1Percentage);  //按 排位1 更新企业产品数据今日量化数
            dailyTradePercentageDao.updateEnterpriseProductDataByRanking(ranking2, ranking2Percentage);  //按 排位2 更新企业产品数据今日量化数
        }
    }

    /**
     * 保存或更新所有企业各ID每日每笔新交易数据的量化数比配置
     */
    @Override
    public Map<String, Object> saveOrUpdateDailyTradePercentage(DailyTradePercentage pojo, Integer updatePerson) {
        if(pojo.getIsEnabled() == null || pojo.getIsEnabled().isEmpty() || !pojo.getIsEnabled().matches("^[01]$")) {
            throw new BusinessException("所有企业各ID每日每笔新交易数据的量化数比开、关不能为空，且只能为0或1");
        }
        if(pojo.getDailyTradePercentage() != null && pojo.getDailyTradePercentage().isEmpty() || !Objects.requireNonNull(pojo.getDailyTradePercentage()).matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("所有企业各ID每日每笔新交易数据的量化数比只能为正整数或小数，且最多保留4位小数");
        }
        if(pojo.getRanking1() != null && !pojo.getRanking1().isEmpty() && !pojo.getRanking1().matches("^[1-9]\\d*$") || Objects.equals(pojo.getRanking1(), "0")) {
            throw new BusinessException("排位1只能为大于0的整数");
        }
        if(pojo.getRanking1Percentage() != null && pojo.getRanking1Percentage().isEmpty() || !Objects.requireNonNull(pojo.getRanking1Percentage()).matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("排位1百分比只能为正整数或小数，且最多保留4位小数");
        }
        if(pojo.getRanking2() != null && !pojo.getRanking2().isEmpty() && !pojo.getRanking2().matches("^[1-9]\\d*$") || Objects.equals(pojo.getRanking2(), "0")) {
            throw new BusinessException("排位2只能为大于0的整数");
        }
        if(pojo.getRanking2Percentage() != null && pojo.getRanking2Percentage().isEmpty() || !Objects.requireNonNull(pojo.getRanking2Percentage()).matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("排位2百分比只能为正整数或小数，且最多保留4位小数");
        }
        int result = dailyTradePercentageDao.saveOrUpdateDailyTradePercentage(pojo, updatePerson);
        if(result > 0){
            return getDailyTradePercentage();
        }else{
            return null;
        }
    }
}
