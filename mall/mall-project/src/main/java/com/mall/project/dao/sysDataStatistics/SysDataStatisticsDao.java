package com.mall.project.dao.sysDataStatistics;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 系统数据统计数据访问对象
 */
@Repository
@Slf4j
public class SysDataStatisticsDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 根据统计名称查询计算方法
     */
    public String getCalculateMethodByDataTypeName(String dataTypeName) {
        String sql = "SELECT service_method FROM sys_data_type WHERE data_type_name = ?";
        try {
            return jdbcTemplate.queryForObject(sql, String.class, dataTypeName);
        } catch (EmptyResultDataAccessException e) {
            //log.warn("未找到数据类型配置: {}", dataTypeName);
            return null;
        }
    }

    /**
     * 判断如果 数据名称 已经存在,则提示 不能再次新增
     */
    public boolean isDataNameExists(String tradeName, int type) {
        String sql = "SELECT EXISTS(SELECT 1 FROM enterprise_data_name WHERE trade_name = ?  and type = ?)";
        return jdbcTemplate.queryForObject(sql, Integer.class, tradeName, type) > 0;
    }

    /**
     * 查看系统已经设置有了哪些数据名称
     */
    public List<Map<String, Object>> getEnterpriseDataName(int type) {
        String sql = "SELECT trade_name from enterprise_data_name where type = ?";
        return jdbcTemplate.queryForList(sql, type);
    }

    /**
     * 统计昨日系统已经设置了的数据名称的合计
     */
    public List<Map<String, Object>> getEnterpriseDataNameList(int type) {
        String sql = "SELECT \n" +
                "    id,\n" +
                "    trade_name,\n" +
                "    trade_amount\n" +
                "FROM enterprise_data_name \n" +
                "WHERE type = ? \n" +
                "    AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY\n" +
                "ORDER BY id DESC";
        try {
            return jdbcTemplate.queryForList(sql, type);
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * 查询常量设置,没有的默认为 1
     * 如果Custom_constants_switch为1(关闭)，constants也为1
     * 只有当Custom_constants_switch为0(开启)时，constants才为实际设置的值
     */
    public String getCustomConstants() {
        try {
            String sql = "SELECT " +
                    "CASE " +
                    "    WHEN Custom_constants_switch = '1' THEN '1' " +
                    "    ELSE COALESCE(CAST(constants AS CHAR), '1') " +
                    "END as constants " +
                    "FROM custom_constants " +
                    "WHERE id = 1";
            return jdbcTemplate.queryForObject(sql, String.class);
        } catch (EmptyResultDataAccessException e) {
            // 如果数据库表没有数据，返回默认值1
            return "1";
        }
    }

    /**
     * 更新 企业数据每日自动更新合计,计算结果 到 Custom_constants 表中
     */
    public void updateCustomConstants(String everydayCount, String result) {
        String sql = "update Custom_constants set everyday_count = ?,result = ?,update_time = NOW() where id = 1";
        jdbcTemplate.update(sql, everydayCount, result);
    }

    /**
     * 更新 量化数 每日自动更新合计 到 Custom_constants 表中
     */
    public void updateCustomConstantsForQuantity(String everydayQuantity) {
        String sql = "update Custom_constants set everyday_quantity = ?,update_time = NOW() where id = 1";
        jdbcTemplate.update(sql, everydayQuantity);
    }

    /**
     * 把计算结果放到 enterprise_data_name 表中
     */
    public void insertDataToEnterpriseDataName(String tradeName, String tradeAmount, int type) {
        String insertSql = "Insert into enterprise_data_name(trade_name,trade_amount,type,update_time)values(?,?,?,now())";
        jdbcTemplate.update(insertSql, tradeName, tradeAmount, type);
    }

    /**
     * 更新 enterprise_data_name 表中的数据
     */
    public void updateDataInEnterpriseDataName(String tradeName, String tradeAmount, int type) {
        String updateSql = "UPDATE enterprise_data_name set trade_amount = ?,update_time = CURDATE() - INTERVAL 1 DAY + INTERVAL 5 MINUTE where trade_name = ? and type = ?";
        jdbcTemplate.update(updateSql, tradeAmount, tradeName, type);
    }
}
