package com.mall.project.service.pushTomallB.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.pushTomallBDao.PushTomallBDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.pushTomallB.PushTomallBService;
import com.mall.project.service.quantityconfig.retry.PushRetryService;
import com.mall.project.service.quantizationValue.QuantizationValueService;
import com.mall.project.util.MallBAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 推送数据到mallB系统服务实现类
 */
@Service
@Slf4j
public class PushTomallBServiceImpl implements PushTomallBService {

    @Autowired
    private PushTomallBDao pushTomallBDao;

    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    @Autowired
    private QuantizationValueService quantizationValueService;

    @Autowired
    private PushRetryService pushRetryService;

    /**
     * 推送量化率到mallB系统
     */
    @Override
    public void pushQuantizationRate() {
        Map<String, Object> dataMap = pushTomallBDao.pushQuantizationRate();
        if (isNotEmpty(dataMap)) {
            try {
                String quantifyRate = dataMap.get("quantify_rate").toString() ;
                String quantifyDate = dataMap.get("quantify_date").toString();
                Map<String, Object> bSettingsMap = pushTomallBDao.pushBSettings();
                String quantifyToCredit = bSettingsMap.get("quantify_to_credit").toString();
                String creditToCoupon = bSettingsMap.get("credit_to_coupon").toString();
                ObjectMapper objectMapper = new ObjectMapper();

                // 构建符合mallB要求的请求体格式
                Map<String, String> jsonObject = new HashMap<>();
                jsonObject.put("quantifyRate", quantifyRate != null ? quantifyRate : "");
                jsonObject.put("quantifyDate", quantifyDate != null ? quantifyDate : "");
                jsonObject.put("quantifyToCredit", quantifyToCredit != null ? quantifyToCredit : "");
                jsonObject.put("creditToCoupon", creditToCoupon != null ? creditToCoupon : "");

                log.info("推送量化率到mallB的数据: {}", objectMapper.writeValueAsString(jsonObject));
                // 直接使用工具类发送POST请求
                ResponseEntity<String> configResponse = mallBAuthUtils.postForEntity("/mall/receptionA/receiveQuantizedData", jsonObject, String.class);

                log.info("推送响应状态: {}", configResponse.getStatusCode());
                log.info("推送响应内容: {}", configResponse.getBody());

                if (configResponse.getStatusCode() != HttpStatus.OK) {
                    throw new BusinessException("推送量化率到mallB系统失败: " + configResponse.getStatusCode());
                }

                // 解析响应
                if (configResponse.getBody() != null) {
                    JsonNode responseNode = objectMapper.readTree(configResponse.getBody());
                    int code = responseNode.path("code").asInt();
                    String msg = responseNode.path("msg").asText();

                    if (code != 200) {
                        throw new BusinessException("推送量化率到mallB系统失败: " + msg);
                    }
                    log.info("推送量化率到mallB系统成功: {}", msg);
                    // 更新推送状态
                    pushTomallBDao.updatePushStatus();
                }

            } catch (Exception e) {
                log.error("推送量化率到mallB系统失败: " + e.getMessage(), e);
                // 启动重试，推送类型2表示量化率
                pushRetryService.startPushRetry(2);

                if (e instanceof BusinessException) {
                    throw (BusinessException) e;
                } else {
                    throw new BusinessException("与mallB系统通信失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 推送B/C授权到mallB系统
     */
    @Override
    public void pushAreaAuthorize() {
        List<Map<String, Object>> dataList = pushTomallBDao.pushAreaAuthorize();
        // 转换下划线格式为驼峰格式
        dataList = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        if (isNotEmpty(dataList)) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                // 创建jsonObject内部数据
                Map<String, Object> jsonObject = new HashMap<>();

                log.info("推送B/C授权到mallB的数据: {}", objectMapper.writeValueAsString(jsonObject));

                // 直接使用工具类发送POST请求
                ResponseEntity<String> configResponse = mallBAuthUtils.postForEntity("/mall/receptionA/receiveAuthorizationData", dataList, String.class);

                log.info("推送响应状态: {}", configResponse.getStatusCode());
                log.info("推送响应内容: {}", configResponse.getBody());

                if (configResponse.getStatusCode() != HttpStatus.OK) {
                    throw new BusinessException("推送B/C授权到mallB系统失败: " + configResponse.getStatusCode());
                }

                // 解析响应
                if (configResponse.getBody() != null) {
                    JsonNode responseNode = objectMapper.readTree(configResponse.getBody());
                    int code = responseNode.path("code").asInt();
                    String msg = responseNode.path("msg").asText();

                    if (code != 200) {
                        throw new BusinessException("推送B/C授权到mallB系统失败: " + msg);
                    }
                    log.info("推送B/C授权到mallB系统成功: {}", msg);
                }
            } catch (Exception e) {
                log.error("推送B/C授权到mallB系统失败: " + e.getMessage(), e);
                // 启动重试，推送类型3表示B/C授权
                pushRetryService.startPushRetry(3);

                if (e instanceof BusinessException) {
                    throw (BusinessException) e;
                } else {
                    throw new BusinessException("与mallB系统通信失败: " + e.getMessage());
                }
            }
        }
    }
    /**
     * 推送量化值到mallB系统
     */
    public void pushQuantizationValue() {
        List<Map<String, Object>> dataList = quantizationValueService.queryTodayQuantizationValue();
        // 转换下划线格式为驼峰格式
        dataList = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        if (isNotEmpty(dataList)) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                // 创建jsonObject内部数据
                Map<String, Object> jsonObject = new HashMap<>();

                log.info("推送量化值到mallB的数据: {}", objectMapper.writeValueAsString(jsonObject));

                // 直接使用工具类发送POST请求
                ResponseEntity<String> configResponse = mallBAuthUtils.postForEntity("/mall/receptionA/batchReceivePush", dataList, String.class);

                log.info("推送响应状态: {}", configResponse.getStatusCode());
                log.info("推送响应内容: {}", configResponse.getBody());

                if (configResponse.getStatusCode() != HttpStatus.OK) {
                    throw new BusinessException("推送量化值到mallB系统失败: " + configResponse.getStatusCode());
                }

                // 解析响应
                if (configResponse.getBody() != null) {
                    JsonNode responseNode = objectMapper.readTree(configResponse.getBody());
                    int code = responseNode.path("code").asInt();
                    String msg = responseNode.path("msg").asText();

                    if (code != 200) {
                        throw new BusinessException("推送量化值到mallB系统失败: " + msg);
                    }
                    log.info("推送量化值到mallB系统成功: {}", msg);
                }
            } catch (Exception e) {
                log.error("推送量化值到mallB系统失败: " + e.getMessage(), e);
                // 启动重试，推送类型4表示量化值
                pushRetryService.startPushRetry(4);

                if (e instanceof BusinessException) {
                    throw (BusinessException) e;
                } else {
                    throw new BusinessException("与mallB系统通信失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 推送平台补贴金到mallB系统
     */
    public void pushPlatformGold() {
        List<Map<String, Object>> dataList = quantizationValueService.queryTodayPlatformGold();
        // 转换下划线格式为驼峰格式
        dataList = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        if (isNotEmpty(dataList)) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                // 创建jsonObject内部数据
                Map<String, Object> jsonObject = new HashMap<>();
                jsonObject.put("exchangeFunds", dataList);

                log.info("推送平台补贴金到mallB的数据: {}", objectMapper.writeValueAsString(jsonObject));

                // 直接使用工具类发送POST请求
                ResponseEntity<String> configResponse = mallBAuthUtils.postForEntity("/mall/receptionA/batchReceiveExchangeFund", jsonObject, String.class);

                log.info("推送响应状态: {}", configResponse.getStatusCode());
                log.info("推送响应内容: {}", configResponse.getBody());

                if (configResponse.getStatusCode() != HttpStatus.OK) {
                    throw new BusinessException("推送平台补贴金到mallB系统失败: " + configResponse.getStatusCode());
                }

                // 解析响应
                if (configResponse.getBody() != null) {
                    JsonNode responseNode = objectMapper.readTree(configResponse.getBody());
                    int code = responseNode.path("code").asInt();
                    String msg = responseNode.path("msg").asText();

                    if (code != 200) {
                        throw new BusinessException("推送平台补贴金到mallB系统失败: " + msg);
                    }
                    log.info("推送平台补贴金到mallB系统成功: {}", msg);
                }
            } catch (Exception e) {
                log.error("推送平台补贴金到mallB系统失败: " + e.getMessage(), e);
                // 启动重试，推送类型6表示平台补贴金
                pushRetryService.startPushRetry(6);
                if (e instanceof BusinessException) {
                    throw (BusinessException) e;
                } else {
                    throw new BusinessException("与mallB系统通信失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 推送 每日Admin量化值 到mallB系统
     */
    public void pushAdminDailyQuantifyValue() {
        try {
            String adminDailyQuantifyValue = quantizationValueService.adminDailyQuantifyValue("");
            ObjectMapper objectMapper = new ObjectMapper();
            // 创建jsonObject内部数据
            Map<String, Object> jsonObject = new HashMap<>();
            jsonObject.put("adminDailyQuantifyValue", adminDailyQuantifyValue);

            log.info("推送每日Admin量化值到mallB的数据: {}", objectMapper.writeValueAsString(jsonObject));

            // 直接使用工具类发送POST请求
            ResponseEntity<String> configResponse = mallBAuthUtils.postForEntity("/mall/receptionA/receiveAdminDailyQuantify", jsonObject, String.class);

            log.info("推送响应状态: {}", configResponse.getStatusCode());
            log.info("推送响应内容: {}", configResponse.getBody());

            if (configResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("推送每日Admin量化值到mallB系统失败: " + configResponse.getStatusCode());
            }

            // 解析响应
            if (configResponse.getBody() != null) {
                JsonNode responseNode = objectMapper.readTree(configResponse.getBody());
                int code = responseNode.path("code").asInt();
                String msg = responseNode.path("msg").asText();

                if (code != 200) {
                    throw new BusinessException("推送每日Admin量化值到mallB系统失败: " + msg);
                }
                log.info("推送每日Admin量化值到mallB系统成功: {}", msg);
            }
        } catch (Exception e) {
            log.error("推送每日Admin量化值到mallB系统失败: " + e.getMessage(), e);
            // 启动重试，推送类型7表示每日Admin量化值
            pushRetryService.startPushRetry(7);

            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            } else {
                throw new BusinessException("与mallB系统通信失败: " + e.getMessage());
            }
        }
    }
}
