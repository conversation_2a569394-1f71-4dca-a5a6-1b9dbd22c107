package com.mall.project.service.functionDatas.impl;

import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.functionDatas.FunctionDatasDao;
import com.mall.project.dto.functionDatas.FunctionDatas;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.functionDatas.FunctionDatasService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 功能数据服务实现类
 */
@Service
public class FunctionDatasServiceImpl implements FunctionDatasService {

    @Autowired
    private FunctionDatasDao functionDatasDao;

    @Override
    public int addFunctionDatas(FunctionDatas pojo, int updatePerson) {
        //验证手机号
        if(isNotEmpty(pojo.getPhone()) && !pojo.getPhone().matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException("手机号格式不正确");
        }
        //验证value 格式
        if(isNotEmpty(pojo.getValue()) && !pojo.getValue().matches("^\\d+(\\.\\d{1,2})?$")) {
            throw new BusinessException("功能数值只能为正整数或小数，且最多保留2位小数");
        }
        return functionDatasDao.addFunctionDatas(pojo, updatePerson);
    }

    @Override
    public CommonPage<Map<String, Object>> queryFunctionDatasPages(String phone, String startDate, String endDate, int pageNum, int pageSize) {
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        //验证开始日期格式 yyyy-MM-dd
        if(isNotEmpty(startDate) && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd");
        }
        //验证结束日期格式 yyyy-MM-dd
        if(isNotEmpty(endDate) && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd");
        }
        List<Map<String, Object>> list = functionDatasDao.queryFunctionDatasPages(phone,  startDate, endDate, pageSize, offset);
        if(list.isEmpty()){
            throw new BusinessException("暂无数据");
        }
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = list.stream().map(ConvertToCamelCase::convertToCamelCase).toList();

        long total = functionDatasDao.queryFunctionDatasCount(phone, startDate, endDate);

        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);

        FunctionDatasServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new FunctionDatasServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("todayTotalFunctionDatas", ConvertToCamelCase.formatDecimal(new BigDecimal(todayTotalFunctionDatas(phone,startDate))));   //今日总功能数值
        summary.put("todayTotalQuantify", ConvertToCamelCase.formatDecimal(new BigDecimal(todayTotalQuantify(phone,startDate))));   //今日总量化值
        summary.put("todayTotalSubsidy", ConvertToCamelCase.formatDecimal(new BigDecimal(todayTotalSubsidy(phone,startDate))));   //今日总补贴金
        summary.put("todayAllTotalFunctionDatas", ConvertToCamelCase.formatDecimal(new BigDecimal(todayAllTotalFunctionDatas(phone,startDate))));   //今日总累计功能数值
        summary.put("todayAllTotalQuantify", ConvertToCamelCase.formatDecimal(new BigDecimal(todayAllTotalQuantify(phone,startDate))));   //今日总累计量化值
        summary.put("todayAllTotalSubsidy", ConvertToCamelCase.formatDecimal(new BigDecimal(todayAllTotalSubsidy(phone,startDate))));   //今日总累计补贴金
        commonPage.setSummary(summary);
        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }

    /**
     * 今日总功能数值
     */
    public String todayTotalFunctionDatas(String phone, String startDate){
        String result = functionDatasDao.todayTotalFunctionDatas(phone,startDate);
        return result == null ? "0" : result;
    }

    /**
     * 今日总量化值
     */
    public String todayTotalQuantify(String phone, String startDate){
        String result = functionDatasDao.todayTotalQuantify(phone,startDate);
        return result == null ? "0" : result;
    }
    /**
     * 今日总补贴金
     */
    @Override
    public String todayTotalSubsidy(String phone, String startDate) {
        String result = functionDatasDao.todayTotalSubsidy(phone,startDate);
        return result == null ? "0" : result;
    }
    /**
     * 今日总累计功能数值
     */
    @Override
    public String todayAllTotalFunctionDatas(String phone, String startDate) {
        String result = functionDatasDao.todayAllTotalFunctionDatas(phone,startDate);
        return result == null ? "0" : result;
    }

    /**
     * 今日总累计量化值
     */
    @Override
    public String todayAllTotalQuantify(String phone, String startDate) {
        String result = functionDatasDao.todayAllTotalQuantify(phone,startDate);
        return result == null ? "0" : result;
    }

    /**
     * 今日总累计补贴金
     */
    @Override
    public String todayAllTotalSubsidy(String phone, String startDate) {
        String result = functionDatasDao.todayAllTotalSubsidy(phone,startDate);
        return result == null ? "0" : result;
    }

    /**
     * 导出功能数据 Excel
     */
    @Override
    public List<Map<String, Object>> exportFunctionDatasExcel(String phone,String startDate, String endDate){
        if(startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if(endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        List<Map<String, Object>> list =  functionDatasDao.exportFunctionDatasExcel(phone,startDate, endDate);
        // 转换下划线格式为驼峰格式
        return list.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }
    /**
     *  将功能数据更新到量化数,这里由定时任务触发,一般在凌晨1点执行
     */
    @Override
    public void updateQuantifyCount() {
        List<Map<String, Object>> dataList = functionDatasDao.updateQuantifyCount();
        for (Map<String, Object> dataMap : dataList) {
            String phone = (String) dataMap.get("phone");
            BigDecimal value = new BigDecimal(dataMap.get("value").toString());
            //1. 更新量化数,使用value 乘以 所有合作企业Admain的各ID每日每笔数据量化数比
            String partnerEnterpriseAdminData = functionDatasDao.getPartnerEnterpriseAdminData();
            if(isNotEmpty(partnerEnterpriseAdminData)){
                BigDecimal dailyDataPercentage = new BigDecimal(partnerEnterpriseAdminData);
                functionDatasDao.updateAdminDailyQuantity(dailyDataPercentage);
            }
            //2. 更新量化数 通过 所有企业各ID每日每笔新交易数据的量化数比 排位 权限
            List<Map<String, Object>> dailyTradePercentageMap = functionDatasDao.getDailyTradePercentage();
            if(isNotEmpty(dailyTradePercentageMap)){
                BigDecimal dailyTradePercentage = new BigDecimal(dailyTradePercentageMap.get(0).get("daily_trade_percentage").toString());  //使用value 乘以 所有企业各ID每日每笔新交易数据的量化数比
                functionDatasDao.updateQuantifyCount(phone, value.multiply(dailyTradePercentage).divide(new BigDecimal(100)));   //更新量化数
                Integer ranking1 = Integer.parseInt(dailyTradePercentageMap.get(0).get("ranking1").toString());                             //排位1
                BigDecimal ranking1Percentage = new BigDecimal(dailyTradePercentageMap.get(0).get("ranking1_percentage").toString());       //排位1设置获取百分比
                Integer ranking2 = Integer.parseInt(dailyTradePercentageMap.get(0).get("ranking2").toString());                             //排位2
                BigDecimal ranking2Percentage = new BigDecimal(dailyTradePercentageMap.get(0).get("ranking2_percentage").toString());       //排位2设置获取百分比
                functionDatasDao.updatemallBUsersCountByRanking(ranking1, ranking1Percentage);                                       //按 排位1 更新量化数
                functionDatasDao.updatemallBUsersCountByRanking(ranking2, ranking2Percentage);                                       //按 排位2 更新量化数
            }
            // 3. 通过 省,市,县、区,镇、街 授权 百分比 更新量化数
            functionDatasDao.updateQuantifyCountByAuthorityPercentage(phone,value);
        }
    }
}
