server:
  port: 9000

spring:
  application:
    name: mall-gateway
  main:
    banner-mode: off  # 关闭启动横幅
  # 配置环境
  profiles:
    active: dev  # 环境：dev=开发测试环境, online=产品上线环境
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: public
        group: DEFAULT_GROUP
    gateway:
      discovery:
        locator:
          enabled: false # 禁用自动服务发现路由，使用明确的路由规则
          lower-case-service-id: true
      routes:
        # 带服务名前缀的路由 - 兼容旧的访问方式
        - id: mall-project-with-prefix
          uri: lb://mall-project
          predicates:
            - Path=/mall-project/**
          filters:
            - StripPrefix=1  # 移除 /mall-project 前缀
        # API路由 - 优先匹配API请求
        - id: mall-project-api
          uri: lb://mall-project
          predicates:
            - Path=/api/**
          filters:
            - StripPrefix=0
        # 其他项目服务路由 - 直接访问
        - id: mall-project-direct
          uri: lb://mall-project
          predicates:
            - Path=/**
          filters:
            - StripPrefix=0
  data:
    redis:
      host: ************
      port: 6379
      password:
      timeout: 5000
      database: 1

# JWT配置
jwt:
  secret: "Gp21h8VE8Eql2azYuKL4vsrOzDNggxP88si8+KrJVC3bVK+qZTU96JrmyxWB6kvGhu9/4VGdmDIYUTZuKNFWUg=="
  expiration: 86400  # Token有效期(秒) 24小时
  tokenHeader: Authorization
  tokenHead: Bearer